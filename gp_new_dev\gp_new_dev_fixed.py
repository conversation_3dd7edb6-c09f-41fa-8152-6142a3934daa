import argparse
import csv
import sys
import time
from datetime import datetime, timedelta, timezone
from dateutil import parser as dateparser
from tqdm import tqdm

# google-play-scraper（Python 版）
# 安装名：google-play-scraper
# 导入名：google_play_scraper
from google_play_scraper import app as gp_app
from google_play_scraper import search as gp_search

def safe_parse_date(text: str):
    """
    将 'Aug 20, 2025' / '2025年8月20日' 等本地化日期解析为日期（无时区，返回 UTC 00:00 的 datetime）。
    解析失败返回 None。
    """
    if not text:
        return None
    try:
        dt = dateparser.parse(text)
        # 只取日期部分，统一到当天的 00:00 UTC，便于比较
        return datetime(dt.year, dt.month, dt.day, tzinfo=timezone.utc)
    except Exception:
        return None

def fetch_new_games(country='us', lang='en', num=200, sleep=0.2):
    """
    使用搜索功能获取新游戏，因为 Python 版本的 google-play-scraper 没有 collection 功能。
    """
    items = []

    # 搜索新游戏的关键词
    search_terms = [
        'new game 2024',
        'new game 2025', 
        'latest game',
        'recent game'
    ]

    for term in search_terms:
        try:
            batch = gp_search(
                term,
                lang=lang,
                country=country,
                n_hits=num // len(search_terms)  # 分配搜索数量
            )
            
            # 过滤出游戏类应用
            games = [app for app in batch if 'game' in app.get('genre', '').lower() or 
                    'game' in app.get('title', '').lower()]
            
            for game in games:
                game["__source_search"] = term
            
            items.extend(games)
            
            # 防止触发风控，轻微 sleep
            time.sleep(sleep)
            
        except Exception as e:
            print(f"搜索 '{term}' 失败: {e}")
            continue

    # 去重（按 appId）
    seen = set()
    deduped = []
    for it in items:
        if it['appId'] not in seen:
            seen.add(it['appId'])
            deduped.append(it)
    return deduped

def enrich_details(app_ids, country='us', lang='en', sleep=0.25):
    """
    对每个 appId 拉取详情（包含 released / updated / installs / ratings 等）。
    返回详情 dict 列表。
    """
    details = []
    for aid in tqdm(app_ids, desc="Fetching app details"):
        try:
            d = gp_app(aid, country=country, lang=lang)
            details.append(d)
        except Exception as e:
            print(f"Failed to fetch details for {aid}: {e}")
        time.sleep(sleep)
    return details

def filter_by_date_range(apps, days_back=30):
    """
    筛选出最近 N 天内发布或更新的游戏。
    """
    cutoff = datetime.now(timezone.utc) - timedelta(days=days_back)
    filtered = []
    
    for app in apps:
        released_date = safe_parse_date(app.get('released'))
        updated_date = safe_parse_date(app.get('updated'))
        
        # 如果发布日期或更新日期在范围内，则保留
        if (released_date and released_date >= cutoff) or \
           (updated_date and updated_date >= cutoff):
            filtered.append(app)
    
    return filtered

def save_to_csv(apps, filename):
    """
    将应用数据保存为 CSV 文件。
    """
    if not apps:
        print("没有数据可保存")
        return
    
    fieldnames = [
        'appId', 'title', 'developer', 'genre', 'score', 'ratings',
        'installs', 'released', 'updated', 'free', 'price'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for app in apps:
            row = {
                'appId': app.get('appId', ''),
                'title': app.get('title', ''),
                'developer': app.get('developer', ''),
                'genre': app.get('genre', ''),
                'score': app.get('score', ''),
                'ratings': app.get('ratings', ''),
                'installs': app.get('installs', ''),
                'released': app.get('released', ''),
                'updated': app.get('updated', ''),
                'free': app.get('free', ''),
                'price': app.get('price', '')
            }
            writer.writerow(row)
    
    print(f"数据已保存到 {filename}")

def main():
    parser = argparse.ArgumentParser(description='获取 Google Play 新游戏')
    parser.add_argument('--country', default='us', help='国家代码 (默认: us)')
    parser.add_argument('--lang', default='en', help='语言代码 (默认: en)')
    parser.add_argument('--num', type=int, default=200, help='搜索数量 (默认: 200)')
    parser.add_argument('--days', type=int, default=30, help='筛选最近N天的游戏 (默认: 30)')
    parser.add_argument('--output', default='new_games.csv', help='输出文件名 (默认: new_games.csv)')
    
    args = parser.parse_args()
    
    print(f"🔍 搜索新游戏...")
    print(f"   国家: {args.country}")
    print(f"   语言: {args.lang}")
    print(f"   数量: {args.num}")
    print(f"   筛选: 最近 {args.days} 天")
    
    # 1. 获取基本游戏列表
    games = fetch_new_games(
        country=args.country,
        lang=args.lang,
        num=args.num
    )
    
    print(f"📱 找到 {len(games)} 个游戏")
    
    if not games:
        print("❌ 没有找到游戏")
        return
    
    # 2. 获取详细信息
    app_ids = [game['appId'] for game in games]
    print(f"📋 获取详细信息...")
    
    detailed_games = enrich_details(app_ids, country=args.country, lang=args.lang)
    print(f"✅ 获取到 {len(detailed_games)} 个游戏的详细信息")
    
    # 3. 按日期筛选
    recent_games = filter_by_date_range(detailed_games, days_back=args.days)
    print(f"🗓️ 筛选出 {len(recent_games)} 个最近 {args.days} 天的游戏")
    
    # 4. 保存结果
    if recent_games:
        save_to_csv(recent_games, args.output)
        
        # 显示前几个结果
        print(f"\n📋 前 10 个结果:")
        for i, game in enumerate(recent_games[:10], 1):
            print(f"{i:2d}. {game.get('title', 'N/A')} - {game.get('developer', 'N/A')}")
            print(f"     发布: {game.get('released', 'N/A')} | 更新: {game.get('updated', 'N/A')}")
    else:
        print("❌ 没有符合条件的游戏")

if __name__ == "__main__":
    main()
