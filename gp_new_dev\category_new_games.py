#!/usr/bin/env python3
"""
使用 Python 的 google-play-scraper 库拉取每个游戏品类的新游戏
"""

import json
import time
import csv
from datetime import datetime, timedelta
from google_play_scraper import search, app
from tqdm import tqdm

# 游戏品类列表 - 基于常见的游戏分类
GAME_CATEGORIES = {
    'action': ['action game', 'fighting game', 'shooter game'],
    'adventure': ['adventure game', 'rpg game', 'role playing'],
    'arcade': ['arcade game', 'retro game', 'classic game'],
    'board': ['board game', 'card game', 'chess'],
    'casual': ['casual game', 'puzzle game', 'match 3'],
    'educational': ['educational game', 'kids game', 'learning'],
    'music': ['music game', 'rhythm game', 'piano'],
    'racing': ['racing game', 'car game', 'driving'],
    'simulation': ['simulation game', 'city builder', 'farming'],
    'sports': ['sports game', 'football', 'basketball'],
    'strategy': ['strategy game', 'tower defense', 'war game'],
    'trivia': ['trivia game', 'quiz game', 'word game']
}

def search_new_games_by_category(category_name, search_terms, max_per_term=25, country='us', lang='en'):
    """
    根据品类搜索新游戏
    """
    print(f"\n🎮 搜索 {category_name} 品类的新游戏...")
    
    all_games = []
    
    for term in search_terms:
        try:
            print(f"  🔍 搜索关键词: '{term}'")
            
            # 搜索游戏
            results = search(
                term,
                lang=lang,
                country=country,
                n_hits=max_per_term
            )
            
            # 过滤游戏类应用
            games = []
            for app_info in results:
                # 检查是否为游戏
                genre = app_info.get('genre', '').lower()
                title = app_info.get('title', '').lower()
                
                if ('game' in genre or 'game' in title or 
                    any(keyword in title for keyword in ['puzzle', 'adventure', 'action', 'racing', 'strategy'])):
                    app_info['search_term'] = term
                    app_info['category'] = category_name
                    games.append(app_info)
            
            print(f"    找到 {len(games)} 个游戏")
            all_games.extend(games)
            
            # 防止被限制
            time.sleep(0.5)
            
        except Exception as e:
            print(f"    ❌ 搜索失败: {e}")
            continue
    
    # 去重
    seen_ids = set()
    unique_games = []
    for game in all_games:
        app_id = game.get('appId')
        if app_id and app_id not in seen_ids:
            seen_ids.add(app_id)
            unique_games.append(game)
    
    print(f"  ✅ {category_name} 品类共找到 {len(unique_games)} 个唯一游戏")
    return unique_games

def get_app_details(app_id, country='us', lang='en'):
    """
    获取应用详细信息
    """
    try:
        details = app(app_id, lang=lang, country=country)
        return details
    except Exception as e:
        print(f"    ❌ 获取 {app_id} 详情失败: {e}")
        return None

def filter_recent_games(games_with_details, days_back=90):
    """
    筛选最近发布或更新的游戏
    """
    cutoff_date = datetime.now() - timedelta(days=days_back)
    recent_games = []
    
    for game in games_with_details:
        if not game:
            continue
            
        # 检查发布日期
        released = game.get('released')
        updated = game.get('updated')
        
        is_recent = False
        
        # 解析发布日期
        if released:
            try:
                if isinstance(released, str):
                    # 尝试解析不同的日期格式
                    for fmt in ['%b %d, %Y', '%Y-%m-%d', '%m/%d/%Y']:
                        try:
                            release_date = datetime.strptime(released, fmt)
                            if release_date >= cutoff_date:
                                is_recent = True
                            break
                        except ValueError:
                            continue
            except:
                pass
        
        # 检查更新日期
        if updated and not is_recent:
            try:
                if isinstance(updated, str):
                    for fmt in ['%b %d, %Y', '%Y-%m-%d', '%m/%d/%Y']:
                        try:
                            update_date = datetime.strptime(updated, fmt)
                            if update_date >= cutoff_date:
                                is_recent = True
                            break
                        except ValueError:
                            continue
            except:
                pass
        
        if is_recent:
            recent_games.append(game)
    
    return recent_games

def save_results(all_results, filename='category_new_games.json'):
    """
    保存结果到文件
    """
    # 保存 JSON
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    # 保存 CSV
    csv_filename = filename.replace('.json', '.csv')
    
    if all_results:
        # 收集所有游戏到一个列表
        all_games = []
        for category, games in all_results.items():
            for game in games:
                game_row = {
                    'category': category,
                    'appId': game.get('appId', ''),
                    'title': game.get('title', ''),
                    'developer': game.get('developer', ''),
                    'genre': game.get('genre', ''),
                    'score': game.get('score', ''),
                    'ratings': game.get('ratings', ''),
                    'installs': game.get('installs', ''),
                    'released': game.get('released', ''),
                    'updated': game.get('updated', ''),
                    'free': game.get('free', ''),
                    'price': game.get('price', ''),
                    'search_term': game.get('search_term', '')
                }
                all_games.append(game_row)
        
        # 写入 CSV
        if all_games:
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = all_games[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_games)
    
    print(f"\n💾 结果已保存:")
    print(f"   JSON: {filename}")
    print(f"   CSV:  {csv_filename}")

def main():
    """
    主函数：拉取每个品类的新游戏
    """
    print("🚀 开始拉取各品类新游戏...")
    
    all_results = {}
    total_games = 0
    
    # 遍历每个品类
    for category, search_terms in GAME_CATEGORIES.items():
        # 搜索该品类的游戏
        games = search_new_games_by_category(category, search_terms)
        
        if not games:
            print(f"  ⚠️ {category} 品类没有找到游戏")
            continue
        
        # 获取详细信息（限制数量避免过多请求）
        print(f"  📋 获取 {category} 品类游戏详情...")
        detailed_games = []
        
        for game in tqdm(games[:20], desc=f"获取{category}详情"):  # 限制每个品类最多20个
            details = get_app_details(game['appId'])
            if details:
                # 合并基本信息和详细信息
                details.update({
                    'search_term': game.get('search_term'),
                    'category': category
                })
                detailed_games.append(details)
            
            time.sleep(0.3)  # 防止被限制
        
        # 筛选最近的游戏
        recent_games = filter_recent_games(detailed_games, days_back=180)  # 6个月内
        
        print(f"  ✅ {category} 品类找到 {len(recent_games)} 个最近6个月的游戏")
        
        all_results[category] = recent_games
        total_games += len(recent_games)
    
    # 保存结果
    save_results(all_results)
    
    # 显示统计
    print(f"\n📊 统计结果:")
    print(f"   总品类数: {len(GAME_CATEGORIES)}")
    print(f"   总游戏数: {total_games}")
    
    for category, games in all_results.items():
        if games:
            print(f"   {category}: {len(games)} 个游戏")
    
    # 显示部分结果
    print(f"\n🎮 部分结果预览:")
    count = 0
    for category, games in all_results.items():
        for game in games[:3]:  # 每个品类显示前3个
            count += 1
            if count > 15:  # 总共显示15个
                break
            print(f"   {count:2d}. [{category}] {game.get('title', 'N/A')} - {game.get('developer', 'N/A')}")
        if count > 15:
            break

if __name__ == "__main__":
    main()
