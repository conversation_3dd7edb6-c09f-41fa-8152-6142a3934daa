import gplay from "google-play-scraper";

// 问题：gplay.collection.NEW_FREE 不存在
// 解决方案：使用以下几种方法获取新游戏

// 方案1：使用搜索功能获取新游戏
const searchNewGames = async () => {
  console.log('=== 方案1：搜索新游戏 ===');
  
  const apps = await gplay.search({
    term: 'new game 2024',           // 搜索关键词，可以包含"new"等词
    num: 50,                         // 返回数量
    //country: 'jp',                 // 选择市场
    //lang: 'zh',                    // 语言
    price: 'free',                   // 'all' | 'free' | 'paid'
    throttle: 5                      // 限速，避免被 503/CAPTCHA
  });
  
  console.log(`找到 ${apps.length} 个游戏:`);
  apps.slice(0, 10).forEach((app, index) => {
    console.log(`${index + 1}. ${app.title} (${app.appId})`);
  });
};

// 方案2：使用现有的collection获取热门免费游戏（可能包含新游戏）
const getTopFreeGames = async () => {
  console.log('\n=== 方案2：热门免费游戏 ===');
  
  const apps = await gplay.list({
    category: gplay.category.GAME,           // 游戏分类
    collection: gplay.collection.TOP_FREE,  // 可用选项：TOP_FREE / TOP_PAID / GROSSING
    //country: 'jp',                         // 选择市场
    //lang: 'zh',                            // 语言
    num: 50,                                 // 返回数量
    throttle: 5                              // 限速，避免被 503/CAPTCHA
  });
  
  console.log(`找到 ${apps.length} 个热门免费游戏:`);
  apps.slice(0, 10).forEach((app, index) => {
    console.log(`${index + 1}. ${app.title} (${app.appId})`);
  });
};

// 方案3：获取详细信息并按发布时间筛选
const getRecentGames = async () => {
  console.log('\n=== 方案3：按发布时间筛选新游戏 ===');
  
  // 先获取一批游戏
  const apps = await gplay.list({
    category: gplay.category.GAME,
    collection: gplay.collection.TOP_FREE,
    num: 20,  // 少量获取，因为需要获取详细信息
    throttle: 5
  });
  
  console.log('正在获取详细信息...');
  
  // 获取每个游戏的详细信息（包含发布时间）
  const detailedApps = [];
  for (let i = 0; i < Math.min(apps.length, 10); i++) {
    try {
      const app = apps[i];
      const details = await gplay.app({
        appId: app.appId,
        throttle: 5
      });
      
      detailedApps.push({
        title: details.title,
        appId: details.appId,
        released: details.released,
        updated: details.updated
      });
      
      console.log(`${i + 1}. ${details.title} - 发布: ${details.released}`);
    } catch (error) {
      console.log(`获取 ${apps[i].title} 详细信息失败:`, error.message);
    }
  }
};

// 方案4：使用特定的游戏子分类
const getGamesByCategory = async () => {
  console.log('\n=== 方案4：按游戏子分类获取 ===');
  
  const categories = [
    { name: '动作游戏', category: gplay.category.GAME_ACTION },
    { name: '街机游戏', category: gplay.category.GAME_ARCADE },
    { name: '休闲游戏', category: gplay.category.GAME_CASUAL }
  ];
  
  for (const cat of categories) {
    try {
      const apps = await gplay.list({
        category: cat.category,
        collection: gplay.collection.TOP_FREE,
        num: 5,
        throttle: 5
      });
      
      console.log(`\n${cat.name}:`);
      apps.forEach((app, index) => {
        console.log(`  ${index + 1}. ${app.title}`);
      });
    } catch (error) {
      console.log(`获取 ${cat.name} 失败:`, error.message);
    }
  }
};

// 执行所有方案
const main = async () => {
  try {
    await searchNewGames();
    await getTopFreeGames();
    await getRecentGames();
    await getGamesByCategory();
  } catch (error) {
    console.error('执行失败:', error);
  }
};

// 运行主函数
main();
