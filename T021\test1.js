const gplay = require('google-play-scraper');
const fs = require('fs');

// 获取所有带 GAME 的游戏品类
const gameCategories = [
  gplay.category.GAME,
  gplay.category.GAME_ACTION,
  gplay.category.GAME_ADVENTURE,
  gplay.category.GAME_ARCADE,
  gplay.category.GAME_BOARD,
  gplay.category.GAME_CARD,
  gplay.category.GAME_CASINO,
  gplay.category.GAME_CASUAL,
  gplay.category.GAME_EDUCATIONAL,
  gplay.category.GAME_MUSIC,
  gplay.category.GAME_PUZZLE,
  gplay.category.GAME_RACING,
  gplay.category.GAME_ROLE_PLAYING,
  gplay.category.GAME_SIMULATION,
  gplay.category.GAME_SPORTS,
  gplay.category.GAME_STRATEGY,
  gplay.category.GAME_TRIVIA,
  gplay.category.GAME_WORD
];

async function scrapeGameDevelopers() {
  try {
    // 读取现有的厂商数据（如果文件存在）
    let existingDevelopers = [];
    const jsonFilePath = 'game_developers.json';

    if (fs.existsSync(jsonFilePath)) {
      try {
        const existingData = fs.readFileSync(jsonFilePath, 'utf8');
        existingDevelopers = JSON.parse(existingData);
        console.log(`读取到现有厂商数据: ${existingDevelopers.length} 个`);
      } catch (error) {
        console.log('读取现有文件失败，将创建新文件');
        existingDevelopers = [];
      }
    } else {
      console.log('未找到现有文件，将创建新文件');
    }

    // 使用 Set 来避免重复，先加入现有数据
    const allDevelopers = new Set(existingDevelopers);
    const initialCount = allDevelopers.size;

    console.log('开始抓取游戏厂商数据...');

    // 遍历所有游戏品类，使用多个collection来获得更多厂商
    const collections = [
      gplay.collection.TOP_FREE,
      gplay.collection.TOP_PAID,
      gplay.collection.GROSSING,
    ];

    for (let i = 0; i < gameCategories.length; i++) {
      const category = gameCategories[i];
      console.log(`正在抓取品类 ${i + 1}/${gameCategories.length}: ${category}`);

      // 对每个品类，尝试多个collection来获得更多厂商
      for (let j = 0; j < collections.length; j++) {
        const collection = collections[j];

        try {
          const results = await gplay.list({
            category: category,
            collection: collection,
            num: 250, // 每个品类每个collection抓取250个
            lang: 'en',
            country: 'us',
            fullDetail: false
          });

          // 提取厂商名称
          results.forEach(game => {
            if (game.developer && game.developer.trim()) {
              allDevelopers.add(game.developer.trim());
            }
          });

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          console.error(`抓取品类 ${category} collection ${collection} 时出错:`, error.message);
          continue; // 继续处理下一个collection
        }
      }

      console.log(`品类 ${category} 完成，当前总厂商数: ${allDevelopers.size}`);
    }

    // 转换为数组
    const developersArray = Array.from(allDevelopers);

    // 保存所有厂商到 JSON 文件（不限制数量，保存所有去重后的厂商）
    fs.writeFileSync(jsonFilePath, JSON.stringify(developersArray, null, 2));

    console.log(`\n抓取完成！`);
    console.log(`原有厂商数: ${initialCount}`);
    console.log(`新增厂商数: ${allDevelopers.size - initialCount}`);
    console.log(`总厂商数: ${allDevelopers.size}`);
    console.log(`已保存所有 ${developersArray.length} 个厂商到 ${jsonFilePath}`);

    // 打印前20个厂商作为预览
    console.log('\n前20个厂商预览:');
    developersArray.slice(0, 20).forEach((developer, index) => {
      console.log(`${index + 1}. ${developer}`);
    });

    // 如果有新增厂商，显示最新添加的一些厂商
    if (allDevelopers.size > initialCount) {
      console.log('\n最新添加的厂商预览:');
      const newDevelopers = developersArray.slice(initialCount, initialCount + 10);
      newDevelopers.forEach((developer, index) => {
        console.log(`${index + 1}. ${developer}`);
      });
    }

  } catch (error) {
    console.error('抓取过程中发生错误:', error);
  }
}

scrapeGameDevelopers();