const gplay = require('google-play-scraper');
const fs = require('fs');
/*
  GAME = 'GAME',
  GAME_ACTION = 'GAME_ACTION',
  GAME_ADVENTURE = 'GAME_ADVENTURE',
  GAME_ARCADE = 'GAME_ARCADE',
  GAME_BOARD = 'GAME_BOARD',
  GAME_CARD = 'GAME_CARD',
  GAME_CASINO = 'GAME_CASINO',
  GAME_CASUAL = 'GAME_CASUAL',
  GAME_EDUCATIONAL = 'GAME_EDUCATIONAL',
  GAME_MUSIC = 'GAME_MUSIC',
  GAME_PUZZLE = 'GAME_PUZZLE',
  GAME_RACING = 'GAME_RACING',
  GAME_ROLE_PLAYING = 'GAME_ROLE_PLAYING',
  GAME_SIMULATION = 'GAME_SIMULATION',
  GAME_SPORTS = 'GAME_SPORTS',
  GAME_STRATEGY = 'GAME_STRATEGY',
  GAME_TRIVIA = 'GAME_TRIVIA',
  GAME_WORD = 'GAME_WORD',
*/
async function scrapeNewGames() {
  try {
    const results = await gplay.list({
      category: gplay.category.GAME_ACTION,
      collection: gplay.collection.TOP_FREE,
      num: 500, // 每次最多抓取 100 条
      lang: 'en',
      country: 'us',
      fullDetail: false // 设置为 true 可获取详细数据，但请求量增加
    });

    // 保存结果到 JSON 文件
    fs.writeFileSync('new_games.json', JSON.stringify(results, null, 2));
    console.log('New games saved to new_games.json');

    // 打印部分结果
    results.forEach((game, index) => {
      console.log(`${index + 1}. ${game.title} by ${game.developer} (${game.scoreText})`);
    });
  } catch (error) {
    console.error('Error fetching new games:', error);
  }
}

scrapeNewGames();