const gplay = require('google-play-scraper');
const fs = require('fs');

// 获取所有带 GAME 的游戏品类
const gameCategories = [
  gplay.category.GAME,
  gplay.category.GAME_ACTION,
  gplay.category.GAME_ADVENTURE,
  gplay.category.GAME_ARCADE,
  gplay.category.GAME_BOARD,
  gplay.category.GAME_CARD,
  gplay.category.GAME_CASINO,
  gplay.category.GAME_CASUAL,
  gplay.category.GAME_EDUCATIONAL,
  gplay.category.GAME_MUSIC,
  gplay.category.GAME_PUZZLE,
  gplay.category.GAME_RACING,
  gplay.category.GAME_ROLE_PLAYING,
  gplay.category.GAME_SIMULATION,
  gplay.category.GAME_SPORTS,
  gplay.category.GAME_STRATEGY,
  gplay.category.GAME_TRIVIA,
  gplay.category.GAME_WORD
];

async function scrapeGameDevelopers() {
  try {
    // 读取现有的厂商数据（如果文件存在）
    let existingDevelopers = {};
    const jsonFilePath = 'game_developers.json';

    if (fs.existsSync(jsonFilePath)) {
      try {
        const existingData = fs.readFileSync(jsonFilePath, 'utf8');
        existingDevelopers = JSON.parse(existingData);
        const existingCount = Object.keys(existingDevelopers).length;
        console.log(`读取到现有厂商数据: ${existingCount} 个厂商`);
      } catch (error) {
        console.log('读取现有文件失败，将创建新文件');
        existingDevelopers = {};
      }
    } else {
      console.log('未找到现有文件，将创建新文件');
    }

    // 使用对象来存储厂商和对应的游戏列表
    const allDevelopers = { ...existingDevelopers };
    const initialCount = Object.keys(allDevelopers).length;

    console.log('开始抓取游戏厂商数据...');

    // 遍历所有游戏品类，使用多个collection来获得更多厂商
    const collections = [
      gplay.collection.TOP_FREE,
      gplay.collection.TOP_PAID,
      gplay.collection.GROSSING,
    ];

    for (let i = 0; i < gameCategories.length; i++) {
      const category = gameCategories[i];
      console.log(`正在抓取品类 ${i + 1}/${gameCategories.length}: ${category}`);

      // 对每个品类，尝试多个collection来获得更多厂商
      for (let j = 0; j < collections.length; j++) {
        const collection = collections[j];

        try {
          const results = await gplay.list({
            category: category,
            collection: collection,
            num: 250, // 每个品类每个collection抓取250个
            lang: 'en',
            country: 'us',
            fullDetail: false
          });

          // 提取厂商名称和游戏名称
          results.forEach(game => {
            if (game.developer && game.developer.trim() && game.title && game.title.trim()) {
              const developer = game.developer.trim();
              const gameTitle = game.title.trim();

              // 如果厂商不存在，创建新的游戏数组
              if (!allDevelopers[developer]) {
                allDevelopers[developer] = [];
              }

              // 避免重复添加同一个游戏
              if (!allDevelopers[developer].includes(gameTitle)) {
                allDevelopers[developer].push(gameTitle);
              }
            }
          });

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          console.error(`抓取品类 ${category} collection ${collection} 时出错:`, error.message);
          continue; // 继续处理下一个collection
        }
      }

      const currentCount = Object.keys(allDevelopers).length;
      console.log(`品类 ${category} 完成，当前总厂商数: ${currentCount}`);
    }

    // 保存所有厂商和游戏数据到 JSON 文件
    fs.writeFileSync(jsonFilePath, JSON.stringify(allDevelopers, null, 2));

    const finalCount = Object.keys(allDevelopers).length;
    const totalGames = Object.values(allDevelopers).reduce((sum, games) => sum + games.length, 0);

    console.log(`\n抓取完成！`);
    console.log(`原有厂商数: ${initialCount}`);
    console.log(`新增厂商数: ${finalCount - initialCount}`);
    console.log(`总厂商数: ${finalCount}`);
    console.log(`总游戏数: ${totalGames}`);
    console.log(`已保存所有数据到 ${jsonFilePath}`);

    // 打印前10个厂商及其游戏作为预览
    console.log('\n前10个厂商及其游戏预览:');
    const developerNames = Object.keys(allDevelopers);
    developerNames.slice(0, 10).forEach((developer, index) => {
      const games = allDevelopers[developer];
      const gameCount = games ? games.length : 0;
      if (gameCount > 0) {
        const gamePreview = games.slice(0, 3).join(', ');
        console.log(`${index + 1}. ${developer} (${gameCount}个游戏): ${gamePreview}${gameCount > 3 ? '...' : ''}`);
      } else {
        console.log(`${index + 1}. ${developer} (0个游戏)`);
      }
    });

    // 如果有新增厂商，显示最新添加的一些厂商
    if (finalCount > initialCount) {
      console.log('\n最新添加的厂商预览:');
      const newDevelopers = developerNames.slice(initialCount, initialCount + 5);
      newDevelopers.forEach((developer, index) => {
        const games = allDevelopers[developer];
        const gameCount = games ? games.length : 0;
        if (gameCount > 0) {
          const gamePreview = games.slice(0, 3).join(', ');
          console.log(`${index + 1}. ${developer} (${gameCount}个游戏): ${gamePreview}${gameCount > 3 ? '...' : ''}`);
        } else {
          console.log(`${index + 1}. ${developer} (0个游戏)`);
        }
      });
    }

  } catch (error) {
    console.error('抓取过程中发生错误:', error);
  }
}

scrapeGameDevelopers();