const gplay = require('google-play-scraper');
const fs = require('fs');

// 获取所有带 GAME 的游戏品类
const gameCategories = [
  gplay.category.GAME,
  gplay.category.GAME_ACTION,
  gplay.category.GAME_ADVENTURE,
  gplay.category.GAME_ARCADE,
  gplay.category.GAME_BOARD,
  gplay.category.GAME_CARD,
  gplay.category.GAME_CASINO,
  gplay.category.GAME_CASUAL,
  gplay.category.GAME_EDUCATIONAL,
  gplay.category.GAME_MUSIC,
  gplay.category.GAME_PUZZLE,
  gplay.category.GAME_RACING,
  gplay.category.GAME_ROLE_PLAYING,
  gplay.category.GAME_SIMULATION,
  gplay.category.GAME_SPORTS,
  gplay.category.GAME_STRATEGY,
  gplay.category.GAME_TRIVIA,
  gplay.category.GAME_WORD
];

async function scrapeGameDevelopers() {
  try {
    const allDevelopers = new Set(); // 使用 Set 来避免重复

    console.log('开始抓取游戏厂商数据...');

    // 遍历所有游戏品类
    for (let i = 0; i < gameCategories.length; i++) {
      const category = gameCategories[i];
      console.log(`正在抓取品类 ${i + 1}/${gameCategories.length}: ${category}`);

      try {
        const results = await gplay.list({
          category: category,
          collection: gplay.collection.TOP_FREE,
          num: 250, // 每个品类抓取250个，确保有足够数据
          lang: 'en',
          country: 'us',
          fullDetail: false
        });

        // 提取厂商名称
        results.forEach(game => {
          if (game.developer && game.developer.trim()) {
            allDevelopers.add(game.developer.trim());
          }
        });

        console.log(`品类 ${category} 完成，当前总厂商数: ${allDevelopers.size}`);

        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`抓取品类 ${category} 时出错:`, error.message);
        continue; // 继续处理下一个品类
      }
    }

    // 转换为数组并取前200个
    const developersArray = Array.from(allDevelopers);
    const top200Developers = developersArray.slice(0, 200);

    // 保存到 JSON 文件
    fs.writeFileSync('game_developers.json', JSON.stringify(top200Developers, null, 2));

    console.log(`\n抓取完成！`);
    console.log(`总共发现 ${allDevelopers.size} 个不同的游戏厂商`);
    console.log(`已保存前 ${top200Developers.length} 个厂商到 game_developers.json`);

    // 打印前20个厂商作为预览
    console.log('\n前20个厂商预览:');
    top200Developers.slice(0, 20).forEach((developer, index) => {
      console.log(`${index + 1}. ${developer}`);
    });

  } catch (error) {
    console.error('抓取过程中发生错误:', error);
  }
}

scrapeGameDevelopers();